import { Injectable } from '@angular/core';
import { Observable, of, forkJoin, throwError } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { MemberProfile } from '../types/member';
import { Program } from '../types/program';
import { MemberService } from './member.service';
import { ProgramService } from './program.service';
import { KeyCloakService } from './key-cloak.service';

export interface PendingSignup {
  profile: MemberProfile;
  selectedProgramId?: string; // Add selected program ID to pending signup
  timestamp: number;
  expiresAt: number;
}

@Injectable({
  providedIn: 'root'
})
export class ProgramMemberService {
  private readonly STORAGE_KEY = 'pending_signup_data';
  private readonly EXPIRY_HOURS = 24; // Pending signups expire after 24 hours

  constructor(
    private memberService: MemberService,
    private programService: ProgramService,
    private keycloakService: KeyCloakService
  ) {}

  /**
   * Store signup data temporarily for multi-tenant flow
   * @param profile Member profile data from signup
   */
  storePendingSignup(profile: MemberProfile): void {
    const pendingSignup: PendingSignup = {
      profile,
      timestamp: Date.now(),
      expiresAt: Date.now() + (this.EXPIRY_HOURS * 60 * 60 * 1000)
    };

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(pendingSignup));
      console.log('Pending signup stored:', pendingSignup.profile.emailAddress);
    } catch (error) {
      console.error('Failed to store pending signup:', error);
      throw new Error('Failed to store signup data');
    }
  }

  /**
   * Retrieve pending signup data
   * @returns Pending signup data or null if expired/not found
   */
  getPendingSignup(): PendingSignup | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const pendingSignup: PendingSignup = JSON.parse(stored);
      
      // Check if expired
      if (Date.now() > pendingSignup.expiresAt) {
        this.clearPendingSignup();
        return null;
      }

      return pendingSignup;
    } catch (error) {
      console.error('Failed to retrieve pending signup:', error);
      this.clearPendingSignup();
      return null;
    }
  }

  /**
   * Clear pending signup data
   */
  clearPendingSignup(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear pending signup:', error);
    }
  }

  /**
   * Store the selected program ID for the pending signup
   * @param programId The selected program ID
   */
  storeSelectedProgram(programId: string): void {
    try {
      const existingSignup = this.getPendingSignup();
      if (existingSignup) {
        // Update existing signup with selected program
        const updatedSignup: PendingSignup = {
          ...existingSignup,
          selectedProgramId: programId
        };
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedSignup));
        console.log('Selected program stored:', programId);
      } else {
        // Create minimal signup data with just the program selection
        // This handles the case where user selects program before any signup data exists
        const minimalSignup: PendingSignup = {
          profile: {} as MemberProfile, // Will be filled later from JWT
          selectedProgramId: programId,
          timestamp: Date.now(),
          expiresAt: Date.now() + (this.EXPIRY_HOURS * 60 * 60 * 1000)
        };
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(minimalSignup));
        console.log('Created new pending signup with selected program:', programId);
      }
    } catch (error) {
      console.error('Failed to store selected program:', error);
    }
  }

  /**
   * Get the stored selected program ID
   * @returns The selected program ID or null if none stored
   */
  getSelectedProgram(): string | null {
    const pendingSignup = this.getPendingSignup();
    return pendingSignup?.selectedProgramId || null;
  }

  /**
   * Set pending signup data
   */
  setPendingSignup(signupData: any): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(signupData));
      console.log('Pending signup data stored for user');
    } catch (error) {
      console.error('Failed to store pending signup:', error);
    }
  }

  /**
   * Check if there's a pending signup for the current user
   */
  hasPendingSignup(): boolean {
    return this.getPendingSignup() !== null;
  }

  /**
   * Create a pending signup from Keycloak user data for multi-tenant flow
   * Used when a user is authenticated but has no member record
   */
  createPendingSignupFromKeycloak(): boolean {
    try {
      const keycloakUser = this.keycloakService.userProfile;
      
      if (!keycloakUser) {
        console.error('No Keycloak user profile available');
        return false;
      }

      // Extract basic profile information from Keycloak token
      const profile: MemberProfile = {
        uniqueId: keycloakUser.sub || '',
        apiId: '', // Will be set based on selected program
        givenNames: keycloakUser.given_name || keycloakUser.name?.split(' ')[0] || '',
        surname: keycloakUser.family_name || keycloakUser.name?.split(' ').slice(1).join(' ') || '',
        emailAddress: keycloakUser.email || '',
        // These will need to be filled during program selection if required
        nationalIdNum: '',
        passortNum: '',
        language: 'en', // Default language
        // Address and telephone arrays - will be empty initially
        personAddress: [],
        personTelephone: [],
      };

      this.storePendingSignup(profile);
      console.log('Created pending signup from Keycloak data for multi-tenant flow');
      return true;

    } catch (error) {
      console.error('Failed to create pending signup from Keycloak data:', error);
      return false;
    }
  }

  /**
   * Complete member registration with selected program
   * @param selectedProgramId The program ID selected by the user (optional, will use stored selection if not provided)
   * @returns Observable of the created member
   */
  completeMemberRegistration(selectedProgramId?: string): Observable<any> {
    return this.completeMemberRegistrationWithData(selectedProgramId);
  }

  /**
   * Complete member registration with selected program and form data
   * @param selectedProgramId The program ID selected by the user (optional, will use stored selection if not provided)
   * @param personalInfoData Personal information data from the form
   * @returns Observable of the created member
   */
  completeMemberRegistrationWithData(selectedProgramId?: string, personalInfoData?: any): Observable<any> {
    // Use provided program ID or fall back to stored selection
    const programId = selectedProgramId || this.getSelectedProgram();
    
    if (!programId) {
      return throwError(() => new Error('No program selected for member registration'));
    }
    let pendingSignup = this.getPendingSignup();
    
    // If no pending signup exists, create one from JWT token data
    if (!pendingSignup) {
      console.log('No pending signup found, creating from JWT token data');
      this.createPendingSignupFromKeycloak();
      pendingSignup = this.getPendingSignup();
      
      if (!pendingSignup) {
        return throwError(() => new Error('Unable to create pending signup data from JWT token'));
      }
    }

    // First, get the program details
    return this.programService.getProgramDetails(programId).pipe(
      switchMap((program: Program | null) => {
        if (!program) {
          throw new Error('Selected program not found');
        }

        // In multi-tenant mode, each program represents a different client
        // For now, we'll use the program ID as the apiId, but this should be 
        // configured based on the actual program-to-client mapping
        const programApiId = this.getProgramApiId(program);
        
        // Use personal info data from form if provided, otherwise fall back to JWT token data
        let memberData;
        if (personalInfoData) {
          // Debug: Check what personalInfoData contains
          console.log('DEBUG: personalInfoData received:', personalInfoData);
          
          // Use form data for member creation
          memberData = {
            givenNames: personalInfoData.givenNames,
            surname: personalInfoData.surname,
            emailAddress: personalInfoData.emailAddress,
            personTelephone: [{
              telephoneType: "CELL",
              countryCode: personalInfoData.countryCode || "+27",
              telephoneNumber: personalInfoData.phoneNumber
            }]
          };
          console.log('DEBUG: memberData after creation:', memberData);
          console.log('Using form data for member creation:', memberData);
        } else {
          // Fall back to JWT token data
          const profileWithProgram = {
            ...pendingSignup.profile,
            apiId: programApiId
          };
          memberData = {
            givenNames: profileWithProgram.givenNames,
            surname: profileWithProgram.surname,
            emailAddress: profileWithProgram.emailAddress,
            personTelephone: profileWithProgram.personTelephone || [{
              telephoneType: "CELL",
              countryCode: "+27",
              telephoneNumber: "000000000"
            }]
          };
          console.log('Using JWT token data for member creation:', memberData);
        }

        console.log('Creating member with program apiId:', programApiId);
        
        // Get user ID for the API path (now should work with fixed getUserIdForApi)
        const userId = this.keycloakService.getUserIdForApi();
        
        if (!userId) {
          return throwError(() => new Error('User ID not available for API call. User may not be authenticated or missing required profile data.'));
        }
        
        console.log('Using userId for API call:', userId);
        
        // Ensure emailAddress is set to userId as per API specification
        memberData.emailAddress = userId;
        console.log('Final member data with corrected emailAddress:', memberData);
        
        // Log the exact payload being sent for debugging
        console.log('=== API CALL DEBUG ===');
        console.log('URL:', `${this.memberService.lssConfig.apiBaseUrl}extsecure/member/${userId}/products/${programId}/member`);
        console.log('Payload being sent:', JSON.stringify(memberData, null, 2));
        console.log('Expected format from Emil:');
        console.log(JSON.stringify({
          "givenNames": "Emil",
          "surname": "van der Merwe", 
          "emailAddress": "{{userId}}",
          "personTelephone": [{
            "telephoneType": "CELL",
            "countryCode": "+27",
            "telephoneNumber": "826728887"
          }]
        }, null, 2));
        console.log('=== END DEBUG ===');
        
        // Use the correct API endpoint as per Emil's documentation:
        // POST /extsecure/member/{userId}/products/{productId}/member
        return (this.memberService as any).httpClient
          .post({
            url: `${this.memberService.lssConfig.apiBaseUrl}extsecure/member/${userId}/products/${programId}/member`,
            key: `register-member-${userId}-${programId}`,
            body: memberData,
          })
          .pipe(
            map((createdMember) => {
              console.log('Member successfully created:', createdMember);
              // Clear pending signup data after successful creation
              this.clearPendingSignup();
              return createdMember;
            }),
            catchError((error) => {
              console.error('=== API ERROR DEBUG ===');
              console.error('Error status:', error.status);
              console.error('Error statusText:', error.statusText);
              console.error('Error message:', error.message);
              console.error('Error body (raw):', error.error);
              console.error('Error body (JSON):', JSON.stringify(error.error, null, 2));
              console.error('Error headers:', error.headers);
              console.error('Error URL:', error.url);
              console.error('Full error object:', error);
              console.error('=== END ERROR DEBUG ===');
              return throwError(() => error);
            })
          );
      })
    );
  }

  /**
   * Get programs available for selection during signup
   * @returns Observable of available programs
   */
  getAvailablePrograms(): Observable<Program[]> {
    // For multi-tenant, we want to show all active programs
    // The user hasn't selected one yet, so we don't filter by user selection
    return this.programService.getAvailablePrograms();
  }

  /**
   * Validate that the user can proceed with the multi-tenant flow
   */
  validateMultiTenantFlow(): Observable<boolean> {
    // Check if user is authenticated via Keycloak
    if (!this.keycloakService.authSuccess) {
      return of(false);
    }

    // Check if user profile requires registration
    if (!this.keycloakService.userProfile?.mustRegister) {
      return of(false);
    }

    // Check if there's pending signup data OR if we need to start the process
    const hasPending = this.hasPendingSignup();
    
    return of(true); // User can proceed with multi-tenant flow
  }

  /**
   * Check if user needs member creation (has Keycloak account but no member record)
   */
  needsMemberCreation(): boolean {
    return this.keycloakService.authSuccess && 
           this.keycloakService.userProfile?.mustRegister === true;
  }

  /**
   * Create a member directly for a specific program using the secure API
   * @param userId The user's Keycloak ID
   * @param productId The selected product/program ID
   * @param memberData The member data (either MemberProfile or API format)
   * @returns Observable of created member
   */
  private createMemberForProgram(userId: string, productId: string, memberData: any): Observable<any> {
    // Use the secure API endpoint: POST /extsecure/member/{userId}/products/{productId}/member
    const apiUrl = `${this.memberService.lssConfig.apiBaseUrl}extsecure/member/${userId}/products/${productId}/member`;
    
    // Check if memberData is already in API format or needs transformation
    let memberPayload;
    if (memberData.givenNames && memberData.surname && memberData.emailAddress && memberData.personTelephone) {
      // Already in API format, use as-is but ensure emailAddress is userId
      memberPayload = {
        ...memberData,
        emailAddress: userId // Always use userId (preferred_username) as emailAddress
      };
    } else {
      // Transform from MemberProfile format to API format
      memberPayload = {
        givenNames: memberData.givenNames || memberData.firstName,
        surname: memberData.surname || memberData.lastName,
        emailAddress: userId, // Use userId (preferred_username) as emailAddress
        personTelephone: memberData.personTelephone || [
          {
            telephoneType: "CELL",
            countryCode: "+27",
            telephoneNumber: "000000000" // Default placeholder
          }
        ]
      };
    }
    
    console.log('Creating member for program via API:', apiUrl);
    console.log('Member payload (minimal format):', memberPayload);
    
    // Use the member service's HTTP client to make the request
    return (this.memberService as any).httpClient.post({
      url: apiUrl,
      key: '',
      body: memberPayload,
    }).pipe(
      map((result: any) => {
        console.log('Member created successfully:', result);
        return result;
      })
    );
  }

  /**
   * Get the apiId for a program (maps program to client)
   * In a real implementation, this would be configured in the environment or fetched from an API
   * @param program The selected program
   * @returns The apiId for member creation
   */
  private getProgramApiId(program: Program): string {
    // For now, create a mapping based on program ID
    // In production, this should come from configuration
    const programApiIdMap: { [key: string]: string } = {
      'loyalty-base': '*********', // Default apiId
      'premium-rewards': '*********',
      'travel-club': '*********',
      'family-benefits': '*********',
      'green-rewards': '*********'
    };

    // Return mapped apiId or use the program ID as fallback
    return programApiIdMap[program.id] || program.id;
  }
}