import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { NotificationItemModel } from 'mobile-components';
import { MemberService, PushNotification, KeyCloakService, MemberProfile } from 'lp-client-api';
// import { AbstractFormComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';
@Component({
  selector: 'app-notifications',
  templateUrl: 'notifications.component.html',
  styleUrls: ['notifications.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
// extends AbstractFormComponent<Statement>
export class NotificationsComponent implements OnInit {
  //loading = false;
  searchRender = false;
  notifications: NotificationItemModel[] = [];
  public rawNotifications: PushNotification[] = [];
  count?: any = 0;
  beginDate?: any;
  endDate?: Date;
  profile?: MemberProfile;

  constructor(
    private memberService: MemberService,
    private kc: KeyCloakService
  ) {}
  environment = environment;

  ngOnInit(): void {
    this.loadMember();
  }

  ionViewDidEnter(): void {
    console.log('ionViewDidEnter called - starting notification search');
    this.search();
  }

  search(): void {
    this.memberService.getNotifications(this.kc.lpUniueReference).subscribe({
      error: (error) => {
        console.error('Error fetching notifications:', error);
        if (error.error && error.error.detail) {
          console.error('Error detail:', error.error.detail);
        }
        // Set empty array to show empty state
        this.notifications = [];
      },
      next: (body) => {
        console.log('🔍 Raw API Response:', body);
        console.log('🔍 Response type:', typeof body);
        console.log('🔍 Is array?', Array.isArray(body));
        
        if (body !== undefined && Array.isArray(body)) {
          console.log('🔍 Array length:', body.length);
          console.log('🔍 First item:', body[0]);
          
          this.rawNotifications = body;
          this.notifications = this.transformNotifications(body);
          
          console.log('✅ Transformed notifications count:', this.notifications.length);
          console.log('✅ Transformed notifications:', this.notifications);
        } else {
          console.warn('❌ Invalid notifications data received:', body);
          this.notifications = [];
        }
        // this.dismissLoadingModal();
      },
    });
    // });
  }

  notiRead(notificationId: any) {
    console.log('not', notificationId);
    this.memberService
.readNotification(notificationId)
      .subscribe({
        error: (error: any) => {
          console.log(error.error.detail);
        },
        next: (body: any) => {
          console.log('body', body);
          let index = this.notifications.findIndex(
            (item: NotificationItemModel) => item.noteSeq === notificationId
          );
          this.notifications.splice(index, 1);
          // Also remove from raw notifications
          let rawIndex = this.rawNotifications.findIndex(
            (item: PushNotification) => item.ID === notificationId
          );
          if (rawIndex >= 0) {
            this.rawNotifications.splice(rawIndex, 1);
          }
          if (body !== undefined) {
            this.count = body;
          }
          // this.dismissLoadingModal();
        },
      });
  }

  toggleNotification(notification: any) {
    notification.expanded = !notification.expanded;
  }

  getNotification(notificationId: any) {
    this.memberService
      .getNotification(this.kc.lpUniueReference, notificationId)
      .subscribe({
        error: (error: any) => {
          console.log(error.error.detail);
        },
        next: (body: any) => {
          console.log('body', body);
          if (body !== undefined) {
            this.count = body;
          }
          // this.dismissLoadingModal();
        },
      });
  }

  async loadMember() {
    // this.showLoadingModal('Fetching your account').then(() => {
    this.memberService
      .getProfile(this.kc.lpUniueReference)
      .subscribe((data: MemberProfile) => {
        this.profile = data;
        // this.dismissLoadingModal();
      });
    // });
  }

  private transformNotifications(pushNotifications: PushNotification[]): NotificationItemModel[] {
    if (!Array.isArray(pushNotifications)) {
      console.warn('transformNotifications: Input is not an array:', pushNotifications);
      return [];
    }
    
    return pushNotifications.map((notification: PushNotification) => {
      if (!notification || !notification.ID) {
        console.warn('Invalid notification object:', notification);
        return null;
      }
      
      return {
        noteSeq: notification.ID,
        shortDescription: this.extractShortDescription(notification.MESSAGE || ''),
        message: notification.MESSAGE || 'No message content'
      };
    }).filter(item => item !== null) as NotificationItemModel[];
  }

  private extractShortDescription(message: string): string {
    if (!message || typeof message !== 'string') {
      return 'No description available';
    }
    
    // Extract short description from message - you can customize this logic
    // Handle both \n and \\n patterns, plus periods
    const shortText = message.split(/[\n\\n\.]/)[0].trim();
    return shortText.length > 50 ? shortText.substring(0, 47) + '...' : shortText;
  }
}
